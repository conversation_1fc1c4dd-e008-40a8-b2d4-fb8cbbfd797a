/**
 * Dashboard Auto-Sync Service
 * 
 * Provides automatic synchronization of Vapi call data to consultation records
 * Ensures briefs tab shows latest call results on dashboard load
 */

import { vapiCallHistoryService } from './vapiCallHistoryService';
import { assistantDataRefreshService } from './assistantDataRefreshService';

class DashboardAutoSyncService {
  constructor() {
    this.syncInProgress = false;
    this.lastSyncTime = null;
    this.syncInterval = 5 * 60 * 1000; // 5 minutes
    this.listeners = new Set();
  }

  /**
   * Add a listener for sync events
   * @param {Function} listener - Callback function
   * @returns {Function} Unsubscribe function
   */
  addListener(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of sync events
   * @param {string} event - Event type
   * @param {Object} data - Event data
   */
  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  /**
   * Perform automatic sync on dashboard load
   * @param {Object} attorney - Attorney data
   * @returns {Promise<Object>} Sync result
   */
  async performDashboardLoadSync(attorney) {
    if (!attorney || !attorney.id || !attorney.vapi_assistant_id) {
      console.log('📋 [DashboardAutoSync] Skipping sync - missing attorney data');
      return { success: false, reason: 'missing_attorney_data' };
    }

    // Check if sync is already in progress
    if (this.syncInProgress) {
      console.log('📋 [DashboardAutoSync] Sync already in progress, skipping');
      return { success: false, reason: 'sync_in_progress' };
    }

    // Check if we've synced recently
    if (this.lastSyncTime && (Date.now() - new Date(this.lastSyncTime).getTime()) < this.syncInterval) {
      console.log('📋 [DashboardAutoSync] Recent sync found, skipping');
      return { success: false, reason: 'recent_sync' };
    }

    try {
      this.syncInProgress = true;
      console.log('🔄 [DashboardAutoSync] Starting dashboard load sync...');

      this.notifyListeners('sync_started', {
        attorneyId: attorney.id,
        assistantId: attorney.vapi_assistant_id,
        timestamp: new Date().toISOString()
      });

      // Step 1: Initialize Vapi call history service
      const initialized = await vapiCallHistoryService.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize Vapi call history service');
      }

      // Step 2: Sync Vapi calls to consultation records
      const syncResult = await vapiCallHistoryService.syncCallsToConsultations(
        attorney.id,
        attorney.vapi_assistant_id,
        { limit: 50 } // Sync last 50 calls
      );

      // Step 3: Refresh assistant data to update UI
      try {
        await assistantDataRefreshService.refreshAssistantData(
          attorney.id,
          attorney.vapi_assistant_id
        );
        console.log('✅ [DashboardAutoSync] Assistant data refreshed');
      } catch (error) {
        console.warn('⚠️ [DashboardAutoSync] Failed to refresh assistant data:', error.message);
      }

      // Update last sync time
      this.lastSyncTime = new Date().toISOString();

      const result = {
        success: true,
        syncResult,
        timestamp: this.lastSyncTime
      };

      this.notifyListeners('sync_completed', {
        attorneyId: attorney.id,
        assistantId: attorney.vapi_assistant_id,
        result,
        timestamp: this.lastSyncTime
      });

      console.log(`✅ [DashboardAutoSync] Dashboard sync completed: ${syncResult.synced} synced, ${syncResult.skipped} skipped, ${syncResult.errors} errors`);
      
      return result;

    } catch (error) {
      console.error('❌ [DashboardAutoSync] Dashboard sync failed:', error);
      
      const result = {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      this.notifyListeners('sync_error', {
        attorneyId: attorney.id,
        assistantId: attorney.vapi_assistant_id,
        error: error.message,
        timestamp: result.timestamp
      });

      return result;
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Force sync regardless of timing constraints
   * @param {Object} attorney - Attorney data
   * @returns {Promise<Object>} Sync result
   */
  async forceDashboardSync(attorney) {
    // Temporarily clear last sync time to force sync
    const originalSyncTime = this.lastSyncTime;
    this.lastSyncTime = null;

    try {
      const result = await this.performDashboardLoadSync(attorney);
      return result;
    } finally {
      // Restore original sync time if sync failed
      if (!this.lastSyncTime) {
        this.lastSyncTime = originalSyncTime;
      }
    }
  }

  /**
   * Sync calls for a specific assistant (used by ConsultationsTab)
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID
   * @returns {Promise<Object>} Sync result
   */
  async syncCallsForAssistant(attorneyId, assistantId) {
    try {
      console.log(`🔄 [DashboardAutoSync] Syncing calls for assistant: ${assistantId}`);

      // Initialize service if needed
      const initialized = await vapiCallHistoryService.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize Vapi call history service');
      }

      // Sync calls
      const syncResult = await vapiCallHistoryService.syncCallsToConsultations(
        attorneyId,
        assistantId,
        { limit: 50 }
      );

      console.log(`✅ [DashboardAutoSync] Assistant sync completed: ${syncResult.synced} synced, ${syncResult.skipped} skipped, ${syncResult.errors} errors`);
      
      return {
        success: true,
        syncResult,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ [DashboardAutoSync] Assistant sync failed:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get sync status information
   * @returns {Object} Sync status
   */
  getSyncStatus() {
    return {
      syncInProgress: this.syncInProgress,
      lastSyncTime: this.lastSyncTime,
      syncInterval: this.syncInterval,
      canSync: !this.syncInProgress && (!this.lastSyncTime || (Date.now() - new Date(this.lastSyncTime).getTime()) >= this.syncInterval)
    };
  }

  /**
   * Clear sync cache and reset state
   */
  resetSyncState() {
    this.syncInProgress = false;
    this.lastSyncTime = null;
    vapiCallHistoryService.clearCache();
    console.log('🔄 [DashboardAutoSync] Sync state reset');
  }

  /**
   * Check if sync is needed based on timing
   * @returns {boolean} Whether sync is needed
   */
  isSyncNeeded() {
    if (this.syncInProgress) return false;
    if (!this.lastSyncTime) return true;
    
    const timeSinceLastSync = Date.now() - new Date(this.lastSyncTime).getTime();
    return timeSinceLastSync >= this.syncInterval;
  }
}

// Export singleton instance
export const dashboardAutoSyncService = new DashboardAutoSyncService();
export default dashboardAutoSyncService;
