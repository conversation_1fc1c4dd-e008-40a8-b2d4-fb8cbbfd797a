{"hash": "10473dbd", "browserHash": "f7e33917", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "60baed8e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c839b4d6", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "e991d503", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0ce101d0", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d6dad7e6", "needsInterop": true}, "@modelcontextprotocol/sdk/client/index.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/index.js", "file": "@modelcontextprotocol_sdk_client_index__js.js", "fileHash": "5efe8655", "needsInterop": false}, "@modelcontextprotocol/sdk/client/sse.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/sse.js", "file": "@modelcontextprotocol_sdk_client_sse__js.js", "fileHash": "d7166f72", "needsInterop": false}, "@modelcontextprotocol/sdk/client/streamableHttp.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js", "file": "@modelcontextprotocol_sdk_client_streamableHttp__js.js", "fileHash": "03072572", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "0df39dbf", "needsInterop": false}, "@vapi-ai/web": {"src": "../../@vapi-ai/web/dist/vapi.js", "file": "@vapi-ai_web.js", "fileHash": "d3cd59fe", "needsInterop": true}, "jose": {"src": "../../jose/dist/webapi/index.js", "file": "jose.js", "fileHash": "30e18a9f", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "f58bae83", "needsInterop": true}, "lodash": {"src": "../../lodash/lodash.js", "file": "lodash.js", "fileHash": "2574ea9e", "needsInterop": true}, "papaparse": {"src": "../../papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "44c445e1", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0f40c938", "needsInterop": true}, "react-dom/server": {"src": "../../react-dom/server.browser.js", "file": "react-dom_server.js", "fileHash": "b4a2a8a9", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "38698972", "needsInterop": false}, "react-icons/md": {"src": "../../react-icons/md/index.mjs", "file": "react-icons_md.js", "fileHash": "8df234d5", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "42000e69", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "c751c3eb", "needsInterop": false}, "rehype-raw": {"src": "../../rehype-raw/index.js", "file": "rehype-raw.js", "fileHash": "c881730c", "needsInterop": false}, "rehype-sanitize": {"src": "../../rehype-sanitize/index.js", "file": "rehype-sanitize.js", "fileHash": "b73fd5ff", "needsInterop": false}, "remark-gfm": {"src": "../../remark-gfm/index.js", "file": "remark-gfm.js", "fileHash": "358b169e", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "b9f07011", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "b46fb016", "needsInterop": false}}, "chunks": {"browser-2H53BKCB": {"file": "browser-2H53BKCB.js"}, "browser-L3WHH2Q2": {"file": "browser-L3WHH2Q2.js"}, "chunk-WLB7R6ZN": {"file": "chunk-WLB7R6ZN.js"}, "chunk-CUTKQHYX": {"file": "chunk-CUTKQHYX.js"}, "chunk-EFHT7PV7": {"file": "chunk-EFHT7PV7.js"}, "chunk-A5ED6EHL": {"file": "chunk-A5ED6EHL.js"}, "chunk-ZKFSI6YS": {"file": "chunk-ZKFSI6YS.js"}, "chunk-3TH3G7JX": {"file": "chunk-3TH3G7JX.js"}, "chunk-Q72EVS5P": {"file": "chunk-Q72EVS5P.js"}, "chunk-73YGM6QR": {"file": "chunk-73YGM6QR.js"}, "chunk-2N3A5BUM": {"file": "chunk-2N3A5BUM.js"}, "chunk-FYR2ONTC": {"file": "chunk-FYR2ONTC.js"}, "chunk-J55MB4ZD": {"file": "chunk-J55MB4ZD.js"}, "chunk-SJSTY3YX": {"file": "chunk-SJSTY3YX.js"}, "chunk-C3M7BXFS": {"file": "chunk-C3M7BXFS.js"}}}