/**
 * Vapi Call History Service
 * 
 * Enhanced service to fetch call history from Vapi API and sync to consultation records
 * Ensures call results show up in briefs tab on dashboard load
 */

import { enhancedVapiMcpService } from './EnhancedVapiMcpService';
import { supabase } from '../lib/supabase';

class VapiCallHistoryService {
  constructor() {
    this.isConnected = false;
    this.lastSyncTime = null;
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Initialize the service and connect to Vapi MCP
   * @param {string} apiKey - Vapi API key
   */
  async initialize(apiKey) {
    try {
      console.log('🔌 [VapiCallHistory] Initializing service...');
      
      if (!apiKey) {
        apiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
      }

      // Connect to Vapi MCP service
      const connected = await enhancedVapiMcpService.connect(apiKey);
      
      if (connected) {
        this.isConnected = true;
        console.log('✅ [VapiCallHistory] Connected to Vapi MCP service');
        return true;
      } else {
        console.error('❌ [VapiCallHistory] Failed to connect to Vapi MCP service');
        return false;
      }
    } catch (error) {
      console.error('❌ [VapiCallHistory] Error initializing service:', error);
      return false;
    }
  }

  /**
   * Fetch call history for a specific assistant from Vapi
   * @param {string} assistantId - The Vapi assistant ID
   * @param {Object} options - Options for fetching calls
   * @returns {Promise<Array>} Array of call objects
   */
  async fetchCallHistoryForAssistant(assistantId, options = {}) {
    try {
      console.log(`📞 [VapiCallHistory] Fetching calls for assistant: ${assistantId}`);

      // Check cache first
      const cacheKey = `calls_${assistantId}`;
      const cached = this.cache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        console.log('📞 [VapiCallHistory] Returning cached calls');
        return cached.data;
      }

      // Ensure we're connected
      if (!this.isConnected) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize Vapi connection');
        }
      }

      // Fetch all calls from Vapi
      const allCalls = await enhancedVapiMcpService.listCalls();
      
      if (!allCalls || !Array.isArray(allCalls)) {
        console.log('📞 [VapiCallHistory] No calls found in Vapi');
        return [];
      }

      // Filter calls for this assistant
      const assistantCalls = allCalls.filter(call => call.assistantId === assistantId);
      
      // Sort by creation date (newest first)
      assistantCalls.sort((a, b) => {
        const dateA = new Date(a.createdAt || a.startedAt || 0);
        const dateB = new Date(b.createdAt || b.startedAt || 0);
        return dateB - dateA;
      });

      // Apply limit if specified
      const limit = options.limit || 50;
      const limitedCalls = assistantCalls.slice(0, limit);

      // Cache the results
      this.cache.set(cacheKey, {
        data: limitedCalls,
        timestamp: Date.now()
      });

      console.log(`✅ [VapiCallHistory] Fetched ${limitedCalls.length} calls for assistant`);
      return limitedCalls;

    } catch (error) {
      console.error('❌ [VapiCallHistory] Error fetching call history:', error);
      return [];
    }
  }

  /**
   * Fetch enhanced call details including transcript and analysis
   * @param {string} callId - The Vapi call ID
   * @returns {Promise<Object|null>} Enhanced call object or null
   */
  async fetchEnhancedCallDetails(callId) {
    try {
      console.log(`🔍 [VapiCallHistory] Fetching enhanced details for call: ${callId}`);

      // Check cache first
      const cacheKey = `call_details_${callId}`;
      const cached = this.cache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        console.log('🔍 [VapiCallHistory] Returning cached call details');
        return cached.data;
      }

      // Ensure we're connected
      if (!this.isConnected) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize Vapi connection');
        }
      }

      // Fetch call details from Vapi
      const callDetails = await enhancedVapiMcpService.getCall(callId);
      
      if (!callDetails) {
        console.log(`📞 [VapiCallHistory] No details found for call: ${callId}`);
        return null;
      }

      // Cache the results
      this.cache.set(cacheKey, {
        data: callDetails,
        timestamp: Date.now()
      });

      console.log(`✅ [VapiCallHistory] Fetched enhanced details for call: ${callId}`);
      return callDetails;

    } catch (error) {
      console.error('❌ [VapiCallHistory] Error fetching call details:', error);
      return null;
    }
  }

  /**
   * Sync Vapi calls to consultation records for an attorney
   * @param {string} attorneyId - The attorney ID
   * @param {string} assistantId - The Vapi assistant ID
   * @param {Object} options - Sync options
   * @returns {Promise<Object>} Sync result
   */
  async syncCallsToConsultations(attorneyId, assistantId, options = {}) {
    try {
      console.log(`🔄 [VapiCallHistory] Syncing calls to consultations for attorney: ${attorneyId}`);

      // Fetch calls from Vapi
      const vapiCalls = await this.fetchCallHistoryForAssistant(assistantId, options);
      
      if (!vapiCalls.length) {
        console.log('📞 [VapiCallHistory] No calls to sync');
        return {
          success: true,
          synced: 0,
          skipped: 0,
          errors: 0,
          details: []
        };
      }

      const result = {
        success: true,
        synced: 0,
        skipped: 0,
        errors: 0,
        details: []
      };

      // Process each call
      for (const call of vapiCalls) {
        try {
          const syncResult = await this.syncSingleCallToConsultation(call, attorneyId);
          
          if (syncResult.success) {
            if (syncResult.created) {
              result.synced++;
              result.details.push({
                callId: call.id,
                status: 'synced',
                consultationId: syncResult.consultationId
              });
            } else {
              result.skipped++;
              result.details.push({
                callId: call.id,
                status: 'skipped',
                reason: 'already_exists'
              });
            }
          } else {
            result.errors++;
            result.details.push({
              callId: call.id,
              status: 'error',
              error: syncResult.error
            });
          }
        } catch (error) {
          result.errors++;
          result.details.push({
            callId: call.id,
            status: 'error',
            error: error.message
          });
        }
      }

      console.log(`✅ [VapiCallHistory] Sync completed: ${result.synced} synced, ${result.skipped} skipped, ${result.errors} errors`);
      this.lastSyncTime = new Date().toISOString();
      
      return result;

    } catch (error) {
      console.error('❌ [VapiCallHistory] Error syncing calls to consultations:', error);
      return {
        success: false,
        error: error.message,
        synced: 0,
        skipped: 0,
        errors: 0,
        details: []
      };
    }
  }

  /**
   * Sync a single Vapi call to a consultation record
   * @param {Object} call - The Vapi call object
   * @param {string} attorneyId - The attorney ID
   * @returns {Promise<Object>} Sync result
   */
  async syncSingleCallToConsultation(call, attorneyId) {
    try {
      // Check if consultation already exists for this call
      const { data: existing } = await supabase
        .from('consultations')
        .select('id')
        .eq('metadata->vapi_call_id', call.id)
        .single();

      if (existing) {
        return {
          success: true,
          created: false,
          consultationId: existing.id
        };
      }

      // Get enhanced call details if available
      let enhancedCall = call;
      try {
        const details = await this.fetchEnhancedCallDetails(call.id);
        if (details) {
          enhancedCall = { ...call, ...details };
        }
      } catch (error) {
        console.warn(`⚠️ [VapiCallHistory] Could not fetch enhanced details for call ${call.id}:`, error.message);
      }

      // Extract client information from call data
      const clientInfo = this.extractClientInfoFromCall(enhancedCall);

      // Create consultation record
      const consultationRecord = {
        attorney_id: attorneyId,
        client_name: clientInfo.name || 'Anonymous Client',
        client_email: clientInfo.email,
        client_phone: clientInfo.phone,
        summary: this.generateSummaryFromCall(enhancedCall),
        transcript: this.extractTranscriptFromCall(enhancedCall),
        duration: enhancedCall.duration || this.calculateCallDuration(enhancedCall),
        practice_area: clientInfo.practice_area,
        location: clientInfo.location,
        location_data: clientInfo.location_data || {},
        metadata: {
          vapi_call_id: call.id,
          assistant_id: call.assistantId,
          vapi_status: call.status,
          vapi_end_reason: call.endedReason,
          tool_executions: enhancedCall.toolExecutions || [],
          messages: enhancedCall.messages || [],
          structured_data: enhancedCall.artifact || enhancedCall.analysis || {},
          source: 'vapi_sync',
          synced_at: new Date().toISOString(),
          created_at: call.createdAt,
          updated_at: call.updatedAt
        },
        status: this.mapVapiStatusToConsultationStatus(call.status)
      };

      // Insert into Supabase
      const { data, error } = await supabase
        .from('consultations')
        .insert(consultationRecord)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        created: true,
        consultationId: data.id
      };

    } catch (error) {
      console.error('❌ [VapiCallHistory] Error syncing single call to consultation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract client information from Vapi call data
   * @param {Object} call - The Vapi call object
   * @returns {Object} Extracted client information
   */
  extractClientInfoFromCall(call) {
    const clientInfo = {
      name: null,
      email: null,
      phone: null,
      practice_area: null,
      location: null,
      location_data: {}
    };

    // Extract from customer data if available
    if (call.customer) {
      clientInfo.name = call.customer.name;
      clientInfo.email = call.customer.email;
      clientInfo.phone = call.customer.phoneNumber || call.customer.phone;
      clientInfo.location = call.customer.location;
      clientInfo.location_data = call.customer.locationData || {};
    }

    // Extract from tool executions if available
    if (call.toolExecutions && Array.isArray(call.toolExecutions)) {
      for (const execution of call.toolExecutions) {
        if (execution.result && typeof execution.result === 'object') {
          // Look for client information in tool results
          if (execution.result.client_name) clientInfo.name = execution.result.client_name;
          if (execution.result.client_email) clientInfo.email = execution.result.client_email;
          if (execution.result.client_phone) clientInfo.phone = execution.result.client_phone;
          if (execution.result.practice_area) clientInfo.practice_area = execution.result.practice_area;
          if (execution.result.location) clientInfo.location = execution.result.location;
        }
      }
    }

    // Extract from structured data if available
    if (call.artifact || call.analysis) {
      const structuredData = call.artifact || call.analysis;
      if (structuredData.client_name) clientInfo.name = structuredData.client_name;
      if (structuredData.client_email) clientInfo.email = structuredData.client_email;
      if (structuredData.client_phone) clientInfo.phone = structuredData.client_phone;
      if (structuredData.practice_area) clientInfo.practice_area = structuredData.practice_area;
      if (structuredData.location) clientInfo.location = structuredData.location;
    }

    return clientInfo;
  }

  /**
   * Generate a summary from Vapi call data
   * @param {Object} call - The Vapi call object
   * @returns {string} Generated summary
   */
  generateSummaryFromCall(call) {
    const duration = call.duration ? Math.round(call.duration / 60) : 0;
    const endReason = call.endedReason || 'unknown';
    const status = call.status || 'unknown';

    let summary = `Call ${status} with reason: ${endReason}. Duration: ${duration} minutes.`;

    // Add structured data summary if available
    if (call.artifact || call.analysis) {
      const structuredData = call.artifact || call.analysis;
      if (structuredData.summary) {
        summary = structuredData.summary;
      } else if (structuredData.issue_description) {
        summary = `Legal consultation regarding: ${structuredData.issue_description}`;
      }
    }

    return summary;
  }

  /**
   * Extract transcript from Vapi call data
   * @param {Object} call - The Vapi call object
   * @returns {string} Extracted transcript
   */
  extractTranscriptFromCall(call) {
    // Try to get transcript from messages
    if (call.messages && Array.isArray(call.messages)) {
      const transcript = call.messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      if (transcript.trim()) {
        return transcript;
      }
    }

    // Try to get transcript from transcript field
    if (call.transcript) {
      return call.transcript;
    }

    // Fallback
    return 'Transcript not available - call ended before completion or transcript not captured.';
  }

  /**
   * Calculate call duration from timestamps
   * @param {Object} call - The Vapi call object
   * @returns {number} Duration in seconds
   */
  calculateCallDuration(call) {
    if (call.duration) {
      return call.duration;
    }

    const startTime = new Date(call.createdAt || call.startedAt);
    const endTime = new Date(call.endedAt || call.updatedAt);

    if (startTime && endTime && endTime > startTime) {
      return Math.round((endTime - startTime) / 1000);
    }

    return 0;
  }

  /**
   * Map Vapi call status to consultation status
   * @param {string} vapiStatus - The Vapi call status
   * @returns {string} Consultation status
   */
  mapVapiStatusToConsultationStatus(vapiStatus) {
    const statusMap = {
      'queued': 'new',
      'ringing': 'new',
      'in-progress': 'in_progress',
      'forwarding': 'in_progress',
      'ended': 'completed',
      'busy': 'failed',
      'no-answer': 'failed',
      'failed': 'failed',
      'canceled': 'canceled'
    };
    return statusMap[vapiStatus] || 'new';
  }

  /**
   * Clear cache for a specific assistant or all cache
   * @param {string} assistantId - Optional assistant ID to clear specific cache
   */
  clearCache(assistantId = null) {
    if (assistantId) {
      const cacheKey = `calls_${assistantId}`;
      this.cache.delete(cacheKey);
      console.log(`🗑️ [VapiCallHistory] Cleared cache for assistant: ${assistantId}`);
    } else {
      this.cache.clear();
      console.log('🗑️ [VapiCallHistory] Cleared all cache');
    }
  }

  /**
   * Get the last sync time
   * @returns {string|null} Last sync time ISO string
   */
  getLastSyncTime() {
    return this.lastSyncTime;
  }

  /**
   * Check if the service is connected
   * @returns {boolean} Connection status
   */
  isServiceConnected() {
    return this.isConnected;
  }
}

// Export singleton instance
export const vapiCallHistoryService = new VapiCallHistoryService();
export default vapiCallHistoryService;
