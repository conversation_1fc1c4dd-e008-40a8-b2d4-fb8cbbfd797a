/* ConsultationsTab.css - Modern Minimal Design */

/* Full-width layout for Briefs tab */
.consultations-tab {
  width: 100%;
  max-width: none;
  padding: 2rem;
  background: var(--bg-primary);
  min-height: 100vh;
  /* Ensure this tab doesn't interfere with sidebar visibility */
  position: relative;
  z-index: 1;
}

.consultations-tab h2 {
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.consultations-tab .tab-description {
  margin: 0 0 2rem 0;
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  font-weight: 400;
  max-width: 600px;
}

/* Enhanced dashboard card for full-width */
.consultations-tab .dashboard-card {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.consultations-tab .dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Consultations header with improved spacing */
.consultations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-filter {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 300px;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary, #6c757d);
  font-size: 0.9rem;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color, #ced4da);
  border-radius: 6px;
  font-size: 0.9rem;
  background-color: var(--input-background, #ffffff);
  color: var(--text-primary, #212529);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color, #D85722);
  box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.1);
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--background-light, #f8f9fa);
  border: 1px solid var(--border-color, #ced4da);
  border-radius: 6px;
  color: var(--text-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.filter-button:hover {
  background-color: var(--background-hover, #e9ecef);
  color: var(--text-primary, #212529);
}

.view-options {
  display: flex;
  gap: 0.25rem;
  background-color: var(--background-light, #f8f9fa);
  border-radius: 6px;
  padding: 0.25rem;
}

.view-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 4px;
  color: var(--text-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-option:hover {
  background-color: var(--background-hover, #e9ecef);
  color: var(--text-primary, #212529);
}

.view-option.active {
  background-color: var(--primary-color, #D85722);
  color: white;
  box-shadow: 0 2px 4px rgba(216, 87, 34, 0.2);
}

/* Modern Table Design */
.consultations-table {
  width: 100%;
  overflow-x: auto;
  border-radius: 16px;
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.consultations-table table {
  width: 100%;
  min-width: 800px;
  border-collapse: separate;
  border-spacing: 0;
  background: transparent;
}

.consultations-table th,
.consultations-table td {
  padding: 1.25rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.consultations-table th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: sticky;
  top: 0;
  z-index: 10;
}

.consultations-table th:first-child {
  border-top-left-radius: 16px;
}

.consultations-table th:last-child {
  border-top-right-radius: 16px;
}

.consultations-table tbody tr {
  transition: all 0.2s ease;
}

.consultations-table tbody tr:hover {
  background: var(--card-hover);
  transform: scale(1.001);
}

.consultations-table tbody tr:hover td {
  border-color: var(--accent-primary);
}

.consultations-table td {
  color: var(--text-primary);
  font-weight: 500;
}

.consultations-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: 16px;
}

.consultations-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: 16px;
}

/* Status badges and action buttons */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.new {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  color: #01579b;
}

.status-badge.in-progress {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #e65100;
}

.status-badge.completed {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: #2e7d32;
}

.status-badge.follow-up {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  color: #ad1457;
}

.table-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 0.125rem;
  font-size: 0.75rem;
}

.table-action-btn:hover {
  background: #87ceeb;
  color: white;
  transform: scale(1.05);
}

.table-action-btn.view {
  background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
  color: white;
}

.table-action-btn.view:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(135, 206, 235, 0.4);
}

.form-success,
.form-error {
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.form-success {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border: 1px solid #10b981;
}

.form-error {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1px solid #ef4444;
}



/* Mobile responsiveness */
@media (max-width: 768px) {
  .consultations-tab {
    padding: 1rem;
  }

  .consultations-tab h2 {
    font-size: 1.5rem;
  }

  .consultations-tab .tab-description {
    font-size: 1rem;
  }

  .consultations-table {
    border-radius: 12px;
  }

  .consultations-table th,
  .consultations-table td {
    padding: 1rem;
    font-size: 0.875rem;
  }

  .table-action-btn {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .consultations-tab {
    padding: 0.75rem;
  }

  .consultations-table th,
  .consultations-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}

.form-success {
  background-color: rgba(0, 128, 0, 0.1);
  color: #006400;
  border: 1px solid rgba(0, 128, 0, 0.2);
}

.form-error {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.condition-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: rgba(0, 123, 255, 0.1);
  color: #0056b3;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  margin-bottom: 10px;
  background-color: rgba(255, 255, 255, 0.5);
}

.rule-info h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
}

.rule-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.rule-conditions {
  display: flex;
  gap: 5px;
}

/* Table Header Actions */
.table-header-actions {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 15px;
}

.table-management-buttons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.table-mgmt-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-primary);
}

.table-mgmt-btn:hover {
  background: var(--card-hover);
  border-color: #87ceeb;
  transform: translateY(-1px);
}

.table-mgmt-btn.import {
  background: linear-gradient(135deg, #87ceeb 0%, #b0e0e6 100%);
  color: #2c3e50;
  border-color: #87ceeb;
}

.table-mgmt-btn.import:hover {
  box-shadow: 0 4px 8px rgba(135, 206, 235, 0.3);
}

.table-mgmt-btn.sync {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-color: #28a745;
}

.table-mgmt-btn.sync:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.table-mgmt-btn.sync:disabled {
  background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
  border-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.table-mgmt-btn.sync .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.custom-column {
  min-width: 120px;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.column-header:hover .column-actions {
  opacity: 1;
}

.column-action-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.8rem;
  color: var(--text-secondary);
  padding: 2px;
}

.column-action-button:hover {
  color: var(--primary-color);
}

/* Modal Styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: var(--radius-medium);
  padding: 25px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-content h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 0.9rem;
}

.form-group small {
  display: block;
  margin-top: 5px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  gap: 8px;
}

.option-item input {
  flex: 1;
}

.option-item button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
}

.option-item button:hover {
  color: #dc3545;
}

.add-option-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: transparent;
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-small);
  cursor: pointer;
  font-size: 0.9rem;
  margin-top: 8px;
}

.add-option-button:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-button,
.save-button {
  padding: 8px 16px;
  border-radius: var(--radius-small);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.save-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.cancel-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.save-button:hover {
  background-color: var(--primary-color-dark);
}

/* Enhanced Consultations Cards */
.consultations-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.consultation-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
}

.consultation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.consultation-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.client-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.client-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
  text-transform: uppercase;
}

.client-details h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.consultation-date {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.consultation-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.completed {
  background-color: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge.pending {
  background-color: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

.status-badge.cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.consultation-card .card-body {
  padding: 1rem 1.5rem;
}

.consultation-summary h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.consultation-summary p {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 0.95rem;
}

.practice-area {
  margin-bottom: 1rem;
}

.practice-area-tag {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  background-color: rgba(var(--primary-color-rgb, 75, 116, 170), 0.1);
  color: var(--primary-color);
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.consultation-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metadata-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metadata-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.contact-icon {
  color: var(--text-secondary);
  width: 16px;
  height: 16px;
}

.consultation-card .card-footer {
  display: flex;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.card-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  flex: 1;
  justify-content: center;
}

.card-action-button.primary {
  background-color: var(--primary-color);
  color: white;
}

.card-action-button.primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.card-action-button.secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.card-action-button.secondary:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.card-action-button:last-child {
  flex: 0 0 auto;
  width: 40px;
  padding: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .consultations-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .consultation-card .card-header {
    padding: 1rem;
  }

  .consultation-card .card-body {
    padding: 0.75rem 1rem;
  }

  .consultation-card .card-footer {
    padding: 0.75rem 1rem;
    flex-direction: column;
  }

  .card-action-button:last-child {
    width: 100%;
  }
}

/* Custom field column styling */
.custom-field-column .column-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.field-source-badge {
  background-color: var(--info-color, #3182ce);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Empty state styling */
.empty-row .empty-cell {
  padding: 40px 20px;
  text-align: center;
  border: none;
}

.empty-state-inline {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--text-secondary);
}

.empty-state-inline .empty-icon {
  font-size: 3rem;
  color: var(--border-color);
  margin-bottom: 8px;
}

.empty-state-inline .empty-content h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.empty-state-inline .empty-content p {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.empty-state-inline .setup-hint {
  background-color: var(--info-bg, rgba(75, 116, 170, 0.1));
  border: 1px solid var(--info-border, rgba(75, 116, 170, 0.2));
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 12px;
  font-size: 0.9rem;
}

.empty-state-cards {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: 40px 20px;
}

/* Responsive design for full-width layout */
@media (max-width: 1200px) {
  .consultations-cards {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .consultations-tab {
    padding: 1rem;
  }

  .consultations-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-filter {
    min-width: auto;
  }

  .view-options {
    align-self: center;
  }

  .consultations-cards {
    grid-template-columns: 1fr;
  }

  .consultation-metadata {
    flex-direction: column;
    gap: 0.5rem;
  }

  .consultations-table th,
  .consultations-table td {
    padding: 0.75rem;
  }
}

/* Dark theme support */
[data-theme="dark"] .consultations-tab {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .consultations-tab h2 {
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .consultations-tab .tab-description {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .consultations-tab .dashboard-card {
  background: var(--dark-card-background, #1e1e1e);
  border-color: var(--dark-border-color, #444);
}

[data-theme="dark"] .search-box input {
  background-color: var(--dark-input-background, #2c2c2c);
  border-color: var(--dark-border-color, #444);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .search-box input:focus {
  border-color: var(--primary-color, #D85722);
  box-shadow: 0 0 0 2px rgba(216, 87, 34, 0.2);
}

[data-theme="dark"] .search-icon {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .filter-button {
  background-color: var(--dark-background-light, #2c2c2c);
  border-color: var(--dark-border-color, #444);
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .filter-button:hover {
  background-color: var(--dark-background-hover, #3c3c3c);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .view-options {
  background-color: var(--dark-background-light, #2c2c2c);
}

[data-theme="dark"] .view-option {
  color: var(--dark-text-secondary, #adb5bd);
}

[data-theme="dark"] .view-option:hover {
  background-color: var(--dark-background-hover, #3c3c3c);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .view-option.active {
  background-color: var(--primary-color, #D85722);
  color: white;
}

[data-theme="dark"] .consultations-table table {
  background: var(--dark-card-background, #1e1e1e);
}

[data-theme="dark"] .consultations-table th {
  background-color: var(--dark-background-light, #2c2c2c);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .consultations-table th,
[data-theme="dark"] .consultations-table td {
  border-bottom-color: var(--dark-border-color, #444);
  color: var(--dark-text-primary, #f8f9fa);
}

[data-theme="dark"] .field-source-badge {
  background-color: var(--info-color-dark, #4299e1);
}

[data-theme="dark"] .empty-state-inline .setup-hint {
  background-color: var(--info-bg-dark, rgba(75, 116, 170, 0.15));
  border-color: var(--info-border-dark, rgba(75, 116, 170, 0.3));
}

/* Screen reader only class for accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Action Dropdown Menu */
.action-dropdown {
  position: relative;
  display: inline-block;
}

.action-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--border-color-light, #e9ecef);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
  z-index: 1000;
  min-width: 200px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  animation: dropdownFadeIn 0.15s ease-out;
  transform-origin: top right;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.action-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  text-align: left;
  color: #374151 !important;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.action-menu-item:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

.action-menu-item:first-child {
  color: #dc2626 !important;
}

.action-menu-item:first-child:hover {
  background-color: #fef2f2 !important;
  color: #b91c1c !important;
}

.action-menu-divider {
  height: 1px;
  background-color: var(--border-color-light, #e9ecef);
  margin: 0.5rem 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--background-light, #f8f9fa);
  border-radius: 4px;
  color: var(--text-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--primary-color, #D85722);
  color: white;
}

/* Responsive adjustments for action menu */
@media (max-width: 768px) {
  .action-menu {
    right: auto;
    left: 0;
  }
}

/* Batch Actions Bar */
.batch-actions-bar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 12px;
  margin-bottom: 1rem;
  animation: slideDown 0.3s ease-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-actions-left {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.batch-info span {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.9rem;
  background: rgba(59, 130, 246, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.clear-selection-btn {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-selection-btn:hover {
  background: var(--card-hover);
  color: var(--text-primary);
}

.batch-workflow-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.workflow-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.workflow-group.pre-transition {
  position: relative;
}

.workflow-group.pre-transition::after {
  content: '';
  position: absolute;
  right: -0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 60%;
  background: linear-gradient(to bottom, transparent, #87ceeb, transparent);
  opacity: 0.6;
}

.workflow-separator {
  width: 2px;
  height: 32px;
  background: linear-gradient(to bottom, transparent, #87ceeb, transparent);
  margin: 0 0.5rem;
  opacity: 0.6;
}

.batch-action-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

/* Pre-engagement workflow buttons - increasing blue gradient */
.batch-action-btn.pre-engagement.qualify {
  background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.batch-action-btn.pre-engagement.intake {
  background: linear-gradient(135deg, #bfdbfe 0%, #60a5fa 100%);
  color: #1d4ed8;
  border: 1px solid #2563eb;
}

.batch-action-btn.pre-engagement.conflict-check {
  background: linear-gradient(135deg, #93c5fd 0%, #3b82f6 100%);
  color: white;
  border: 1px solid #1d4ed8;
}

.batch-action-btn.pre-engagement.collect-info {
  background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
  color: white;
  border: 1px solid #1e40af;
}

.batch-action-btn.pre-engagement.refer {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: 1px solid #1e40af;
}

/* Client engagement workflow buttons - increasing orange/tan gradient */
.batch-action-btn.client-engagement.draft {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  color: #9a3412;
  border: 1px solid #ea580c;
}

.batch-action-btn.client-engagement.review {
  background: linear-gradient(135deg, #fdba74 0%, #fb923c 100%);
  color: #9a3412;
  border: 1px solid #dc2626;
}

.batch-action-btn.client-engagement.research {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
  color: white;
  border: 1px solid #c2410c;
}

.batch-action-btn.client-engagement.file {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  color: white;
  border: 1px solid #9a3412;
}

.batch-action-btn.client-engagement.forms {
  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
  color: white;
  border: 1px solid #991b1b;
}

.batch-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Selection Column */
.select-column {
  width: 40px;
  text-align: center;
  padding: 0.75rem 0.5rem !important;
}

.select-all-btn,
.select-row-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 1rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-all-btn:hover,
.select-row-btn:hover {
  background: var(--card-hover);
  color: #87ceeb;
  transform: scale(1.1);
}

.selected-row {
  background: rgba(135, 206, 235, 0.1) !important;
  border-left: 3px solid #87ceeb;
}

.selected-row:hover {
  background: rgba(135, 206, 235, 0.15) !important;
}

/* Enhanced Action Menu */
.action-menu-section-title {
  padding: 0.5rem 1rem 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 0.25rem;
}

.action-menu-item.workflow-action {
  color: #4682b4 !important;
}

.action-menu-item.workflow-action:hover {
  background-color: rgba(135, 206, 235, 0.1) !important;
  color: #2c3e50 !important;
}

.action-menu-item.workflow-action.pre-transition {
  border-left: 3px solid #87ceeb;
  padding-left: 0.75rem;
}

.action-menu-item.workflow-action.client-engagement {
  border-left: 3px solid #4caf50;
  padding-left: 0.75rem;
}

.action-menu-item.workflow-action.client-engagement {
  color: #2e7d32 !important;
}

.action-menu-item.workflow-action.client-engagement:hover {
  background-color: rgba(76, 175, 80, 0.1) !important;
  color: #1b5e20 !important;
}

/* Mobile Responsiveness for Batch Actions */
@media (max-width: 768px) {
  .batch-actions-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .batch-workflow-actions {
    justify-content: center;
  }

  .batch-action-btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }

  .select-column {
    width: 32px;
  }

  .select-all-btn,
  .select-row-btn {
    font-size: 0.875rem;
  }
}

/* Subtle styling differences for workflow phases */
.workflow-group.pre-transition .batch-action-btn {
  border: 1px solid rgba(135, 206, 235, 0.3);
}

.workflow-group.client-engagement .batch-action-btn {
  border: 1px solid rgba(76, 175, 80, 0.3);
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}
