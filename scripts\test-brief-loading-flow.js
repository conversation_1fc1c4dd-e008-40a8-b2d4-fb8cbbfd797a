/**
 * Test Brief Loading Flow
 * 
 * This script tests the complete flow from authentication to brief display,
 * ensuring call results appear correctly in the briefs tab.
 */

import { vapiCallHistoryService } from '../src/services/vapiCallHistoryService.js';
import { dashboardAutoSyncService } from '../src/services/dashboardAutoSync.js';
import { assistantDataRefreshService } from '../src/services/assistantDataRefreshService.js';

// Test configuration
const TEST_CONFIG = {
  // Use the default assistant ID from memories
  assistantId: '6de64756-653a-4446-a0af-e8d704e1d630',
  // Mock attorney ID for testing
  attorneyId: 'test-attorney-123',
  // Test timeout
  timeout: 30000 // 30 seconds
};

/**
 * Mock attorney data for testing
 */
const mockAttorney = {
  id: TEST_CONFIG.attorneyId,
  vapi_assistant_id: TEST_CONFIG.assistantId,
  firm_name: 'Test Law Firm',
  email: '<EMAIL>'
};

/**
 * Test the Vapi call history service
 */
async function testVapiCallHistoryService() {
  console.log('🧪 Testing Vapi Call History Service...');
  
  try {
    // Test initialization
    const initialized = await vapiCallHistoryService.initialize();
    if (!initialized) {
      throw new Error('Failed to initialize Vapi call history service');
    }
    console.log('✅ Vapi call history service initialized');

    // Test fetching call history
    const calls = await vapiCallHistoryService.fetchCallHistoryForAssistant(
      TEST_CONFIG.assistantId,
      { limit: 10 }
    );
    
    console.log(`✅ Fetched ${calls.length} calls for assistant`);
    
    if (calls.length > 0) {
      console.log('📞 Sample call data:', {
        id: calls[0].id,
        status: calls[0].status,
        assistantId: calls[0].assistantId,
        createdAt: calls[0].createdAt
      });
    }

    return { success: true, callCount: calls.length };
  } catch (error) {
    console.error('❌ Vapi call history service test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test the dashboard auto-sync service
 */
async function testDashboardAutoSyncService() {
  console.log('🧪 Testing Dashboard Auto-Sync Service...');
  
  try {
    // Test sync status
    const status = dashboardAutoSyncService.getSyncStatus();
    console.log('📊 Sync status:', status);

    // Test dashboard load sync
    const syncResult = await dashboardAutoSyncService.performDashboardLoadSync(mockAttorney);
    
    if (syncResult.success) {
      console.log('✅ Dashboard auto-sync completed successfully');
      console.log('📊 Sync results:', {
        synced: syncResult.syncResult?.synced || 0,
        skipped: syncResult.syncResult?.skipped || 0,
        errors: syncResult.syncResult?.errors || 0
      });
    } else {
      console.log('⚠️ Dashboard auto-sync skipped:', syncResult.reason);
    }

    return { success: true, syncResult };
  } catch (error) {
    console.error('❌ Dashboard auto-sync service test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test the assistant data refresh service
 */
async function testAssistantDataRefreshService() {
  console.log('🧪 Testing Assistant Data Refresh Service...');
  
  try {
    // Test refreshing assistant data
    const refreshedData = await assistantDataRefreshService.refreshAssistantData(
      TEST_CONFIG.attorneyId,
      TEST_CONFIG.assistantId
    );

    console.log('✅ Assistant data refreshed successfully');
    console.log('📊 Refreshed data summary:', {
      assistantName: refreshedData.assistant?.name || 'Unknown',
      callCount: refreshedData.calls?.length || 0,
      consultationCount: refreshedData.consultations?.length || 0,
      stats: refreshedData.stats
    });

    return { success: true, refreshedData };
  } catch (error) {
    console.error('❌ Assistant data refresh service test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test the complete brief loading flow
 */
async function testCompleteBriefLoadingFlow() {
  console.log('🧪 Testing Complete Brief Loading Flow...');
  
  try {
    // Step 1: Test Vapi call history service
    const callHistoryResult = await testVapiCallHistoryService();
    if (!callHistoryResult.success) {
      throw new Error(`Call history test failed: ${callHistoryResult.error}`);
    }

    // Step 2: Test dashboard auto-sync service
    const autoSyncResult = await testDashboardAutoSyncService();
    if (!autoSyncResult.success) {
      throw new Error(`Auto-sync test failed: ${autoSyncResult.error}`);
    }

    // Step 3: Test assistant data refresh service
    const refreshResult = await testAssistantDataRefreshService();
    if (!refreshResult.success) {
      throw new Error(`Data refresh test failed: ${refreshResult.error}`);
    }

    // Step 4: Validate data flow
    const callsFromHistory = callHistoryResult.callCount;
    const callsFromRefresh = refreshResult.refreshedData.calls?.length || 0;
    
    console.log('📊 Data flow validation:', {
      callsFromHistory,
      callsFromRefresh,
      dataConsistent: callsFromHistory === callsFromRefresh
    });

    console.log('✅ Complete brief loading flow test passed!');
    return {
      success: true,
      results: {
        callHistory: callHistoryResult,
        autoSync: autoSyncResult,
        dataRefresh: refreshResult
      }
    };
  } catch (error) {
    console.error('❌ Complete brief loading flow test failed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Brief Loading Flow Tests...');
  console.log('=====================================');
  
  const startTime = Date.now();
  
  try {
    // Set timeout for the entire test suite
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Test timeout')), TEST_CONFIG.timeout);
    });

    // Run the complete test flow
    const testPromise = testCompleteBriefLoadingFlow();
    
    const result = await Promise.race([testPromise, timeoutPromise]);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('=====================================');
    if (result.success) {
      console.log(`✅ All tests passed! Duration: ${duration}ms`);
      console.log('🎉 Brief loading flow is working correctly');
    } else {
      console.log(`❌ Tests failed! Duration: ${duration}ms`);
      console.log(`Error: ${result.error}`);
    }
    
    return result;
  } catch (error) {
    console.error('❌ Test runner failed:', error);
    return { success: false, error: error.message };
  }
}

// Export for use in other scripts
export { runTests, testVapiCallHistoryService, testDashboardAutoSyncService, testAssistantDataRefreshService };

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}
